<template>
  <z-paging-swiper :fixed="true" :swiper-style="swiper" safe-area-inset-bottom>
    <template #top>
      <CustomNavBar :fixed="false">
        <template #left>
          <scroll-view
            :scroll-into-view="scrollIntoView"
            :scroll-left="scrollLeft"
            :scroll-with-animation="false"
            :scroll-x="true"
            class="scroll-view"
          >
            <view :class="{ 'no-animation': !animationEnabled }" class="scroll-content">
              <view
                v-for="(item, index) in tagList"
                :id="`tab-${index}`"
                :key="index"
                :class="{
                  'tab-active': selectIndex == index,
                  'tab-inactive': selectIndex != index,
                }"
                class="content_list_for"
                @click="handSelect(index, item)"
              >
                <view
                  :class="selectIndex == index ? 'title' : ' c-#888 font-weight-500'"
                  class="text-ellipsis relative tab-text p-r-40rpx"
                >
                  {{ truncateText(item.expectedPositions, 6) }}
                  <view
                    :class="{
                      'indicator-show': selectIndex == index,
                      'indicator-hide': selectIndex != index,
                    }"
                    class="tab-indicator"
                  >
                    <wd-img :height="14" :src="tabs" :width="14" class="indicator-icon" />
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </template>
        <template #right>
          <view class="flex justify-right items-center w-100">
            <image
              class="w-50rpx h-50rpx m-r-10rpx"
              mode="aspectFill"
              src="/static/img/edit.png"
              @click="goJobExpectations"
            ></image>
          </view>
        </template>
        <template #content>
          <view class="page-top">
            <view class="content_search_list content_search-p-t">
              <view class="content_search_list_flex">
                <view
                  class="content_list_left content_list_left-w"
                  style="display: flex; flex-direction: row"
                >
                  <view
                    v-for="(item, index) in typeList"
                    :key="index"
                    :class="{
                      'type-tab-active': selectTag === index,
                      'type-tab-inactive': selectTag !== index,
                    }"
                    class="content_list_left_for type-tab-item"
                    @click="handType(index)"
                  >
                    <view class="type-tab-content">
                      <view
                        :class="
                          selectTag === index
                            ? 'content_list_left_color'
                            : 'content_list_left_color1'
                        "
                        class="type-tab-text"
                      >
                        {{ item.name }}
                      </view>
                      <view
                        :class="{
                          'indicator-visible': selectTag === index,
                          'indicator-hidden': selectTag !== index,
                        }"
                        class="type-tab-indicator"
                      ></view>
                    </view>
                  </view>
                  <view class="content_list_adress" @click="goFilter">
                    <view class="text-28rpx c-#666" style="white-space: nowrap">筛选</view>
                    <wd-icon color="#999" name="caret-down-small" size="18px"></wd-icon>
                  </view>
                </view>

                <view class="content_list_adress-1 m-t-[-10rpx]" @click="searchPosition">
                  <view class="text-24rpx c-#333">{{ truncateText(cityName, 6) }}</view>

                  <wd-img :height="12" :src="location" :width="12" />
                </view>
              </view>
            </view>
          </view>
        </template>
      </CustomNavBar>
    </template>

    <!-- Swiper 容器 -->
    <swiper
      :current="selectIndex"
      :duration="300"
      :easing-function="'easeInOutCubic'"
      class="swiper-container"
      @change="onSwiperChange"
    >
      <swiper-item v-for="(item, index) in tagList" :key="index" class="swiper-item">
        <z-paging-swiper-item
          :ref="(el) => setPagingRef(el, index)"
          :current-index="selectIndex"
          :tab-index="index"
          @query="queryList"
          @updateList="updateList"
        >
          <!-- 位置筛选区域 -->
          <view v-if="selectTag === 1" class="bg_flex-1">
            <view
              v-for="(posItem, posIndex) in positionList"
              :key="posIndex"
              :class="activeChange === posIndex ? 'activePositin' : 'nomalPositin'"
              class="bg_box"
              @click="changePosition(posIndex)"
            >
              {{ posItem.name }}
            </view>
          </view>
          <JobCardList
            :index="index"
            :isShow="true"
            :job-list="pageDataList[index] || []"
            @go-detail="goDetail"
            @go-job="goJob"
            @go-chat="goChat"
          />
        </z-paging-swiper-item>
      </swiper-item>
    </swiper>

    <template #bottom>
      <customTabbar name="home" />
    </template>
  </z-paging-swiper>
  <godHorse
    v-if="params.entity.provinceCode"
    :params="{
      cityCode: params.entity.cityCode,
      districtCode: !params.entity.districtCode ? null : params.entity.districtCode,
      provinceCode: params.entity.provinceCode,
    }"
    @handleSentResumes="handleSentResumes"
  ></godHorse>
  <resumes-sent
    v-model:show="sentResumesBool"
    :params="{
      cityCode: params.entity.cityCode,
      districtCode: !params.entity.districtCode ? null : params.entity.districtCode,
      positionCode: params.entity.expectedPositionsCode,
      provinceCode: params.entity.provinceCode,
    }"
  />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBarBig.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import { getqueryList, getmyAddress, positionInfoByjob } from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import { useLoginStore, useResumeStore } from '@/store'
import { ChatUIKit } from '@/ChatUIKit/index'
import resumesSent from '@/components/common/resumes-sent.vue'
import JobCardList from '@/components/home/<USER>/index.vue'
import { truncateText } from '@/utils/util'
import location from '@/static/img/location.png'
import godHorse from '@/components/god-horse/index.vue'
import ppIcon from '@/static/img/ppIcon.png'
import tabs from '@/static/img/tabs.png'

const { getDictLabel } = useDictionary()
const { userRoleIsBusiness } = useUserInfo()
defineOptions({
  name: 'HomePersonal',
})
const message = useMessage()
const appUserStore = ChatUIKit.appUserStore
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const { bool: sentResumesBool, setTrue: sentResumesBoolTrue } = useBoolean()
const { setmyjobList } = usePosition()
const { pageInfo, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const swiper = ref({
  background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
})
// 多个 z-paging-swiper-item 实例的引用
const pagingRefs = ref([])
const setPagingRef = (el, index) => {
  if (el) {
    pagingRefs.value[index] = el
  }
}
const pageDataList = ref([])

// 每个 tab 的 loading 状态 - 用于控制骨架屏显示
const loadingList = ref([])
// 每个 tab 是否已经加载过数据 - 用于区分首次加载和后续加载
const hasLoadedList = ref([])

// 计算当前tab的数据条数，用于控制骨架屏显示数量
const getCurrentTabDataCount = (index: number) => {
  const data = pageDataList.value[index] || []
  return Math.min(data.length, 5) // 最多显示5条
}

// 获取当前tab应该显示的骨架屏数量
const getSkeletonCount = (index: number) => {
  const dataCount = (pageDataList.value[index] || []).length
  // 骨架屏数量为数据条数，最多3个
  return Math.min(dataCount, 3)
}

const tab = ref(0)
const myjobList = []
const resumeStore = useResumeStore()
// vuex数据
const loginStore = useLoginStore()
const title = ref('')
const activeChange = ref(0)
const cityName = ref('')
const homeCity = ref({})

const params = reactive({
  orderBy: {},
  entity: {
    // 省
    provinceName: '',
    provinceCode: '',
    // 市
    cityName: '',
    cityCode: '',
    // 区
    districtName: '',
    districtCode: '',
    // 关键字
    keyword: '',
    baseInfoId: null,
    expectedCity: '',
    expectedCityCode: '',
    expectedIndustry: '',
    expectedIndustryCode: '',
    expectedPositions: '',
    expectedPositionsCode: '',
    jobType: null,
    salaryExpectationEnd: null,
    salaryExpectationStart: null,
    workEducational: null,
    isNews: null,
    isRecruit: null,
    distanceMeters: null,
    lon: null,
    lat: null, // 尾度
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workExperienceStart: '',
    workExperienceEnd: '',
  },
})
const selectTag = ref(0)
const selectIndex = ref(0)
// 控制动画是否启用，避免初始化时的抖动
const animationEnabled = ref(false)
const typeList = ref([
  {
    name: '推荐',
    value: '1',
  },
  {
    name: '附近',
    value: '2',
  },
  {
    name: '最新',
    value: '3',
  },
  {
    name: '急招',
    value: '4',
  },
])
// 求职期望
const goJobExpectations = () => {
  uni.navigateTo({
    url: '/resumeRelated/jobExpectations/index?isShow=' + 'home',
  })
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
// 去沟通item
const goChat = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, item)
    }
  } catch (error) {}
}
const tagList = ref([])

async function handleSentResumes() {
  try {
    await userToRealName()
    sentResumesBoolTrue()
  } catch (error) {}
}

const goJob = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      const num = await sendResumeMessage(hxUserInfoVO.username, item)
      if (!num) {
        message
          .confirm({
            title: '提示',
            msg: '请完善简历后再投递',
          })
          .then(() => {
            uni.navigateTo({
              url: '/resumeRelated/AttachmentResume/index',
            })
          })
          .catch()
      }
    }
  } catch (error) {}
}
const goFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index?type=toPerson',
  })
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
// 地址搜索
const searchPosition = () => {
  uni.navigateTo({
    url: '/resumeRelated/HomeRegion/index',
  })
}
// 附近
const positionList = ref([
  {
    name: '不限',
  },
  {
    name: '5km',
  },
  {
    name: '10km',
  },
  {
    name: '20km',
  },
  {
    name: '30km',
  },
])
// 获取3个岗位
const jobList = async () => {
  const res: any = await positionInfoByjob()
  if (res.code === 0 && res.data && Array.isArray(res.data) && res.data.length > 0) {
    tagList.value = res.data
    console.log('设置tagList:', tagList.value)

    params.entity.expectedPositions = tagList.value[0]?.expectedPositions || ''
    params.entity.expectedPositionsCode = tagList.value[0]?.expectedPositionsCode || ''
    title.value = tagList.value[0]?.expectedPositions || ''
    getRegenList()
  } else {
    console.log('positionInfoByjob返回数据无效:', res)
  }
}

// 附近距离切换
const changePosition = (index: number) => {
  activeChange.value = index
  params.entity.distanceMeters = null
  if (index === 0) {
    params.entity.distanceMeters = null
  }
  if (index === 1) {
    params.entity.distanceMeters = 5 * 1000
  }
  if (index === 2) {
    params.entity.distanceMeters = 10 * 1000
  }
  if (index === 3) {
    params.entity.distanceMeters = 20 * 1000
  }
  if (index === 4) {
    params.entity.distanceMeters = 30 * 1000
  }
  // 重新加载当前选中的 tab 数据
  nextTick(() => {
    if (
      pagingRefs.value &&
      Array.isArray(pagingRefs.value) &&
      selectIndex.value >= 0 &&
      selectIndex.value < pagingRefs.value.length
    ) {
      const currentPagingRef = pagingRefs.value[selectIndex.value]
      if (currentPagingRef && typeof currentPagingRef.reload === 'function') {
        // 设置 loading 状态
        loadingList.value[selectIndex.value] = true
        currentPagingRef.reload()
      }
    }
  })
}

// 切换列表
const handType = (index: number) => {
  selectTag.value = index
  params.entity.isNews = null
  params.entity.distanceMeters = null
  params.entity.isRecruit = null

  if (selectTag.value === 1) {
    getmyAddressList()
  }
  if (selectTag.value === 2) {
    params.entity.isNews = 1
  }
  if (selectTag.value === 3) {
    params.entity.isRecruit = 1
  }
  // 重新加载当前选中的 tab 数据
  nextTick(() => {
    if (
      pagingRefs.value &&
      Array.isArray(pagingRefs.value) &&
      selectIndex.value >= 0 &&
      selectIndex.value < pagingRefs.value.length
    ) {
      const currentPagingRef = pagingRefs.value[selectIndex.value]
      if (currentPagingRef && typeof currentPagingRef.reload === 'function') {
        // 设置 loading 状态
        loadingList.value[selectIndex.value] = true
        currentPagingRef.reload()
      }
    }
  })
}
// 获取地址
const getmyAddressList = async () => {
  const res: any = await getmyAddress()
  if (res.code === 0) {
    if (res.data?.lat) {
      params.entity.lat = res.data.lat
      params.entity.lon = res.data.lon
    } else {
      uni.navigateTo({
        url: '/setting/AdressMange/index',
      })
    }
  }
}

const getRegenList = () => {
  // 设置查询条件
  const filter = resumeStore?.fillterObg
  if (filter) {
    params.entity.workSalaryBegin = filter.workSalaryBegin
    params.entity.workSalaryEnd = filter.workSalaryEnd
    params.entity.sizeName = filter.sizeName
    params.entity.workEducational = filter.workEducational
    params.entity.workExperienceStart = filter.workExperienceStart
    params.entity.workExperienceEnd = filter.workExperienceEnd
    params.entity.jobType = filter.jobType
  }

  // 获取城市选中-第一个
  console.log(loginStore.homeJobAvtive, loginStore.homeCity1)
  if (loginStore.homeJobAvtive === 0) {
    if (loginStore.homeCity1?.cityName || loginStore.homeCity1?.districtCode) {
      // 省
      params.entity.provinceName = loginStore.homeCity1.provinceName
      params.entity.provinceCode = loginStore.homeCity1.provinceCode
      // 市
      params.entity.cityName = loginStore.homeCity1?.cityName
      params.entity.cityCode = loginStore.homeCity1?.cityCode
      // 区
      params.entity.districtName = loginStore.homeCity1?.districtName
      params.entity.districtCode = loginStore.homeCity1?.districtCode
      cityName.value = loginStore.homeCity1.districtName
        ? loginStore.homeCity1?.districtName
        : loginStore.homeCity1?.cityName
    } else {
      loginStore.sethomeCity1({
        provinceName: tagList.value[0].provinceName,
        provinceCode: tagList.value[0].provinceCode,
        cityCode: tagList.value[0].cityCode,
        cityName: tagList.value[0].cityName,
        districtCode: '',
        districtName: '',
      })
      params.entity.provinceName = tagList.value[0].provinceName
      params.entity.provinceCode = tagList.value[0].provinceCode
      params.entity.cityName = tagList.value[0].cityName
      params.entity.cityCode = tagList.value[0].cityCode
      params.entity.districtName = ''
      params.entity.districtCode = ''
      cityName.value = tagList.value[0].cityName
    }
  } else if (loginStore.homeJobAvtive === 1) {
    if (loginStore.homeCity2?.provinceName) {
      // 省
      params.entity.provinceName = loginStore.homeCity2.provinceName
      params.entity.provinceCode = loginStore.homeCity2.provinceCode
      // 市
      params.entity.cityName = loginStore.homeCity2.cityName
      params.entity.cityCode = loginStore.homeCity2.cityCode
      // 区
      params.entity.districtName = loginStore.homeCity2.districtName
      params.entity.districtCode = loginStore.homeCity2.districtCode
      cityName.value = loginStore.homeCity2.districtName
        ? loginStore.homeCity2.districtName
        : loginStore.homeCity2.cityName
    } else {
      loginStore.sethomeCity2({
        provinceName: tagList.value[1].provinceName,
        provinceCode: tagList.value[1].provinceCode,
        cityCode: tagList.value[1].cityCode,
        cityName: tagList.value[1].cityName,
        districtCode: '',
        districtName: '',
      })
      params.entity.provinceName = tagList.value[1].provinceName
      params.entity.provinceCode = tagList.value[1].provinceCode
      params.entity.cityName = tagList.value[1].cityName
      params.entity.cityCode = tagList.value[1].cityCode
      params.entity.districtName = ''
      params.entity.districtCode = ''
      cityName.value = tagList.value[1].cityName
    }
  } else {
    if (loginStore.homeCity3?.provinceName) {
      // 省
      params.entity.provinceName = loginStore.homeCity3.provinceName
      params.entity.provinceCode = loginStore.homeCity3.provinceCode
      // 市
      params.entity.cityName = loginStore.homeCity3.cityName
      params.entity.cityCode = loginStore.homeCity3.cityCode
      // 区
      params.entity.districtName = loginStore.homeCity3.districtName
      params.entity.districtCode = loginStore.homeCity3.districtCode
      cityName.value = loginStore.homeCity3.districtName
        ? loginStore.homeCity3.districtName
        : loginStore.homeCity3.cityName
    } else {
      loginStore.sethomeCity3({
        provinceName: tagList.value[2].provinceName,
        provinceCode: tagList.value[2].provinceCode,
        cityCode: tagList.value[2].cityCode,
        cityName: tagList.value[2].cityName,
        districtCode: '',
        districtName: '',
      })
      params.entity.provinceName = tagList.value[2].provinceName
      params.entity.provinceCode = tagList.value[2].provinceCode
      params.entity.cityName = tagList.value[2].cityName
      params.entity.cityCode = tagList.value[2].cityCode
      params.entity.districtName = ''
      params.entity.districtCode = ''
      cityName.value = tagList.value[2].cityName
    }
  }

  // 使用 nextTick 确保 DOM 更新完成后再执行
  nextTick(() => {
    // 重新加载当前选中的 tab 数据
    if (
      pagingRefs.value &&
      Array.isArray(pagingRefs.value) &&
      selectIndex.value >= 0 &&
      selectIndex.value < pagingRefs.value.length &&
      pagingRefs.value[selectIndex.value] &&
      typeof pagingRefs.value[selectIndex.value].reload === 'function'
    ) {
      // 设置 loading 状态
      loadingList.value[selectIndex.value] = true
      pagingRefs.value[selectIndex.value].reload()
    }
  })
}
const onSwiperChange = async (e: any) => {
  const newIndex = e.detail.current
  if (newIndex !== selectIndex.value) {
    const targetItem = tagList.value[newIndex]
    if (targetItem) {
      // 更新选中状态
      selectIndex.value = newIndex
      loginStore.sethomeJobAvtive(newIndex)
      title.value = targetItem.expectedPositions

      // 设置期望岗位参数
      params.entity.expectedPositions = targetItem.expectedPositions
      params.entity.expectedPositionsCode = targetItem.expectedPositionsCode
      await getRegenList()

      // 滚动到对应的tab位置
      scrollToTab(newIndex)

      // 如果该 tab 还没有数据，则加载数据
      if (pagingRefs.value[newIndex] && typeof pagingRefs.value[newIndex].reload === 'function') {
        pagingRefs.value[newIndex].reload()
      }
    }
  }
}

// 滚动到指定tab位置，确保选中项完全可见
const scrollLeft = ref(0)
const scrollIntoView = ref('')
const scrollToTab = (index: number) => {
  nextTick(() => {
    // 使用scroll-into-view确保选中项完全可见
    scrollIntoView.value = `tab-${index}`
    // 清空scroll-into-view，避免影响后续操作
    setTimeout(() => {
      scrollIntoView.value = ''
    }, 100) // 缩短延时，提高响应速度
  })
}

const handSelect = async (index: number, item: any) => {
  console.log('handSelect called:', index, item.expectedPositions)

  selectIndex.value = index
  loginStore.sethomeJobAvtive(index)
  title.value = item.expectedPositions

  // 设置期望岗位参数
  params.entity.expectedPositions = item.expectedPositions
  params.entity.expectedPositionsCode = item.expectedPositionsCode
  await getRegenList()
  // 滚动到对应的tab位置
  scrollToTab(index)

  // 重新加载对应 tab 的数据
  if (pagingRefs.value[index] && typeof pagingRefs.value[index].reload === 'function') {
    // 设置 loading 状态
    loadingList.value[index] = true
    pagingRefs.value[index].reload()
  }
}
onMounted(async () => {
  await uni.$onLaunched
  await nextTick()
  loginStore.sethomeJobAvtive(selectIndex.value)
  await jobList()

  // 初始化时设置滚动位置
  setTimeout(() => {
    scrollToTab(selectIndex.value)
  }, 100)
})
uni.$on('refresh-a-page', getRegenList)
uni.$on('refresh-a-jobList', jobList)
const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  // 设置当前 tab 的期望岗位，添加安全检查
  const currentTag = tagList.value[selectIndex.value]
  if (currentTag && currentTag.expectedPositions) {
    params.entity.expectedPositions = currentTag.expectedPositions
    params.entity.expectedPositionsCode = currentTag.expectedPositionsCode
    title.value = currentTag.expectedPositions
  }
  try {
    const res: any = await getqueryList({
      ...params,
      size: pageInfo.size,
      page: pageInfo.page,
    })

    console.log('API返回数据:', res)

    if (res.code === 0 && res.data && res.data.list) {
      // 匹配度和竞争力
      setmyjobList(res.data.list)
      // 先处理所有数据，确保数据完整性
      const processedList = await Promise.all(
        res.data.list.map(async (ele: any, index: number) => {
          // console.log(`处理第${index}条数据:`, ele)

          // 匹配度和竞争力处理
          loginStore.myjobFillterList.forEach((item) => {
            if (item.id === ele.id) {
              ele.competitiveness = item.competitiveness
              ele.matchingDegree = item.matchingDegree
              ele.count = item.count
            }
          })

          // 在线状态处理
          try {
            const eleData = await appUserStore.getUserInfoFromStore(ele.hxUserInfoVO?.username)
            if (ele.hxUserInfoVO) {
              ele.hxUserInfoVO.isOnline = !!eleData?.isOnline
            }
          } catch (error) {}
          // 头像处理
          if (!ele.hrPositionUrl) {
            ele.hrPositionUrl =
              ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
          }

          // 公司规模处理
          ele.sizeName = await getDictLabel(100, ele.sizeName)
          // 学历处理
          ele.workEducational = await getDictLabel(10, ele.workEducational)

          // 安全处理 positionKey，确保它是一个数组
          if (ele.positionKey && typeof ele.positionKey === 'string') {
            ele.positionKey = ele.positionKey.split(',').filter((item) => item && item.trim())
          } else if (!Array.isArray(ele.positionKey)) {
            ele.positionKey = []
          }
          if (ele.welfareTreatment && ele.welfareTreatment.length > 0) {
            ele.positionKey = [...ele.welfareTreatment, ...ele.positionKey]
          }
          if (
            ele.workExperienceStart === 0 &&
            ele.workExperienceEnd === 0 &&
            Array.isArray(ele.positionKey)
          ) {
            // 处理工作年限
            ele.positionKey.unshift('经验不限')
          } else {
            ele.positionKey.unshift(`${ele.workExperienceStart}-${ele.workExperienceEnd}年`)
          }
          // 学历处理
          if (ele.workEducational === '全部' && Array.isArray(ele.positionKey)) {
            ele.positionKey.unshift('学历不限')
          } else {
            ele.positionKey = [ele.workEducational, ...ele.positionKey]
          }

          // 安全处理 count，确保它是一个数组
          if (!Array.isArray(ele.count)) {
            ele.count = []
          }

          // 薪资处理
          ele.workSalaryBegin =
            ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
          ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')

          // 距离处理
          if (ele.distanceMeters) {
            const distance = Math.floor(parseInt(ele.distanceMeters) / 1000)
            if (distance === 0) {
              ele.distanceMeters = '<1km'
            } else {
              ele.distanceMeters = distance + 'km'
            }
          }

          // console.log(`第${index}条数据处理完成:`, ele)
          return ele
        }),
      )

      pagingRefs.value[selectIndex.value].complete(processedList)
    } else {
      // 如果请求失败，清空当前tab的数据
      pageDataList.value[selectIndex.value] = []
    }
  } catch (error) {}
}

// 更新列表数据的方法
const updateList = (data: any[]) => {
  // 更新当前对应tab的数据
  pageDataList.value[selectIndex.value] = data
  // pagingRefs.value[selectIndex.value].reload()
}

const homeShow = async () => {
  if (resumeStore.isRefresh === 1) {
    await jobList()
    resumeStore.setIsRefresh(0)
  }
}
defineExpose({
  homeShow,
})
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;

  .wd-tabs__nav {
    background: transparent;
  }

  .wd-tabs__nav-item {
    &.is-active {
      flex: none;
      margin-right: 40rpx;
      font-size: 48rpx;
    }
  }
}

:deep(.custom-progress) {
  width: 180rpx !important;
  padding: 10rpx 3rpx;
}

:deep(.wd-progress__label) {
  font-size: 22rpx !important;
}

:deep(.wd-progress__outer) {
  height: 12rpx;
  background: #fff;
  border-radius: 10rpx;
}

:deep(.wd-progress__inner) {
  border-radius: 10rpx;
}

:deep(.wd-progress__inner) {
  background: linear-gradient(to left, #ff5e5e, #ffc5d6, #fdd1d1) !important;
}

.border-top {
  border-top: 2rpx dashed #d8d8d8;
}

.progress-style {
  padding: 0rpx 10rpx;
  border: 1rpx solid #ff3636;
  border-radius: 10rpx;
}

.style-italic {
  font-style: italic;
}

// Border闪烁动画
/* 标签文字过渡 - 移除动画避免抖动 */
.tab-text {
  position: relative;
  font-size: 36rpx !important;
  transition:
    font-size 0.2s ease,
    color 0.2s ease; /* 只对字体大小和颜色做过渡 */
  transform-origin: center;
  backface-visibility: hidden; /* 防止闪烁 */
  /* 选中状态 */
  &.title {
    font-size: 48rpx !important;
    font-weight: 500;
    color: #000000;
  }
}

.shtop {
  padding-top: -20rpx;
}

.custom-class {
  background: transparent;
}

.content_list_left-w {
  width: calc(100% - 200rpx);
}

.content_search-p {
  padding: 30rpx 40rpx;
}

::v-deep .uni-scroll-view-content {
  text-align: left !important;
}

.stateType {
  padding: 0rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #888888;
  text-align: center;
  border: 1rpx solid #888888;

  border-radius: 6rpx;
}

.sx {
  background: #adbaff;
}

.jz {
  background: #fda283;
}

/* 水平滚动容器样式 */
.scroll-view {
  width: 100%;
  /* 确保滚动容器有足够的内边距，避免选中项被遮挡 */
  // padding: 0 20rpx;
  white-space: nowrap;
}

.scroll-content {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  white-space: nowrap;
}

.content_list_for {
  display: inline-block;
  flex-shrink: 0; /* 防止元素被压缩 */
  margin-right: 10rpx; /* 添加右边距 */
  white-space: nowrap;
  vertical-align: top;
  transform: translateZ(0); /* 启用硬件加速 */
  backface-visibility: hidden; /* 防止闪烁 */
  /* 移除缩放动画，避免抖动和隐藏问题 */
  &.tab-active {
    transform: translateZ(0); /* 移除缩放效果 */

    .tab-text {
      transition:
        font-size 0.2s ease,
        color 0.2s ease; /* 只对字体和颜色做过渡 */
    }
  }

  &.tab-inactive {
    transform: translateZ(0);

    .tab-text {
      transition:
        font-size 0.2s ease,
        color 0.2s ease; /* 只对字体和颜色做过渡 */
    }
  }

  .content_list_border_1 {
    padding-right: 30rpx;
    padding-left: 30rpx;
  }
}

/* 标签指示器动画 - 优化抖动 */
.tab-indicator {
  position: absolute;
  right: 20rpx;
  bottom: -14rpx;
  transition: all 0.2s ease-out; /* 缩短动画时长 */
  will-change: opacity, transform; /* 优化渲染性能 */
  backface-visibility: hidden; /* 防止闪烁 */

  &.indicator-show {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  &.indicator-hide {
    opacity: 0;
    transform: translateY(5rpx) scale(0.9); /* 减少位移和缩放幅度 */
  }

  .indicator-icon {
    transition: all 0.2s ease-out; /* 统一动画时长 */
  }
}

/* 移除点击反馈动画，避免抖动 */
/* 页面初始化时禁用动画，避免从tabbar切换时的抖动 */
.scroll-view {
  /* 确保滚动容器稳定 */
  -webkit-overflow-scrolling: touch;
}

.scroll-content {
  /* 防止初始化时的布局抖动 */
  min-height: 60rpx;
  /* 页面刚加载时禁用动画 */
  &.no-animation {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}

/* 移除复杂的关键帧动画，使用简单的transition避免抖动 */
.content_list_for {
  /* 移除可能导致抖动的关键帧动画，改用简单的transition */
  &.tab-active {
    .tab-text {
      /* 使用transition而不是animation，避免抖动 */
      transition: all 0.25s ease-out;
    }
  }

  &.tab-inactive {
    .tab-text {
      /* 使用transition而不是animation，避免抖动 */
      transition: all 0.25s ease-out;
    }
  }
}

.job-tag {
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #fff;
  background: #ff5151;
  border-radius: 20rpx 0rpx 20rpx 0rpx;
}

.content_search-p-t {
  padding: 0rpx 30rpx 0rpx;
}

.page_left_1 {
  font-size: 24rpx !important;
  line-height: 44rpx;
  color: #666;
}

/* 公司信息容器样式 */
.company-info-container {
  flex: 1;
  min-width: 0; /* 允许flex子项收缩 */
  overflow: hidden; /* 隐藏溢出内容 */
}

/* 公司名称样式 */
.company-name {
  flex: 1;
  min-width: 0;
  max-width: 60%; /* 限制最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分隔符样式 */
.separator {
  flex-shrink: 0; /* 防止分隔符被压缩 */
  margin: 0 8rpx;
}

/* 公司规模样式 */
.company-size {
  flex-shrink: 0; /* 防止公司规模被压缩 */
  max-width: 30%; /* 限制最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  padding-top: 5rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 5rpx 20rpx;
    // margin: 14rpx 0;
    margin-right: 15rpx;
    font-size: 22rpx;
    font-weight: 400;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.bg_flex-1 {
  z-index: 1000;
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 20rpx;
  margin-top: 0rpx;

  .activePositin {
    color: #4d8fff;
    border: 1rpx solid #4d8fff;
  }

  .nomalPositin {
    color: #000;
  }

  .bg_box {
    padding: 0rpx 20rpx;
    // margin: 14rpx 0;
    margin-right: 22rpx;
    font-size: 24rpx;
    font-weight: 400;
    line-height: 44rpx;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.content_list_adress {
  display: flex;
  flex-direction: row;
  align-items: flex-center;
  font-size: 24rpx;
  font-weight: 400;
  color: #555555;
}

.content_list_adress-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.content_search_list_flex {
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  padding-bottom: 30rpx;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 48rpx;
  font-weight: 500;
  color: #000000;
}

/* 文本省略样式 - 超过6个字显示省略号 */
.text-ellipsis {
  display: inline-block;
  max-width: 12em; /* 6个字的宽度，1个中文字符约等于2em */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content_list_right-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 84rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

  ._img {
    width: 44rpx;
    height: 44rpx;
  }
}

.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}

/* 类型标签容器样式 */
.content_list_left_for {
  display: flex;
  flex-direction: row;
  padding-right: 30rpx;
  border-bottom: 6rpx solid transparent;
}

/* 新增的类型标签样式 - 简化过渡效果，避免抖动 */
.type-tab-item {
  position: relative;
  /* 移除复杂的动画，只保留简单过渡 */
  transition: none;
  transform: translateZ(0); /* 启用硬件加速 */

  &.type-tab-active {
    /* 移除缩放效果，避免抖动 */
    transform: translateZ(0);
  }

  &.type-tab-inactive {
    transform: translateZ(0);
  }
}

.type-tab-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.type-tab-text {
  font-size: 26rpx;
  /* 简化过渡效果，只对颜色和字体大小做过渡 */
  transition:
    color 0.2s ease,
    font-size 0.2s ease;
  transform-origin: center;
}

.type-tab-indicator {
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  width: 58rpx;
  height: 6rpx;
  background: #ff9191;
  border-radius: 3rpx;
  /* 简化过渡效果，只对透明度做过渡 */
  transition: opacity 0.2s ease;
  transform: translateX(-50%);
  transform-origin: center;

  &.indicator-visible {
    opacity: 1;
  }

  &.indicator-hidden {
    opacity: 0;
  }
}

.content_list_left_color {
  font-size: 28rpx !important;
  font-weight: 600;
  color: #000000;
  /* 移除缩放效果，避免抖动 */
}

.content_list_left_color1 {
  font-size: 28rpx !important;
  font-weight: 400;
  color: #666666;
  /* 移除缩放效果，避免抖动 */
}

/* 移除点击反馈动画和关键帧动画，避免抖动 */

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    position: relative;
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 100rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    ._img {
      position: absolute;
      right: 10rpx;
      width: 30rpx;
      height: 30rpx;
    }

    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: left;
      width: 100%;
      padding: 10rpx 60rpx 10rpx 20rpx;
      white-space: nowrap;
      // background: red;
    }
  }
}

.select_border {
  width: 100%;
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.select_noBorder {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #3e3e56;
  text-align: center;
}

.content_list_left_xian {
  width: 58rpx;
  height: 8rpx;
  font-weight: bold;
  background: #ff9191;
  border-radius: 2rpx 2rpx 2rpx 2rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;

        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.content {
  // height: 100%;
  // background-color: rgba(244, 244, 244, 1);
  width: 100%;
}

.start_ICON {
  ._icon {
    width: 56rpx;
    height: 56rpx;
  }
}

/* 滑动指示器样式 */
.swipe-indicator {
  position: fixed;
  top: 50%;
  z-index: 9999;
  pointer-events: none;
  transition: opacity 0.2s ease;
  transform: translateY(-50%);

  &.swipe-next {
    right: 40rpx;
  }

  &.swipe-prev {
    left: 40rpx;
  }

  .swipe-indicator-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .swipe-indicator-text {
      margin-top: 8rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: #4d8fff;
    }
  }
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* z-paging-swiper 样式 */
.swiper-container {
  width: 100%;
  height: 100%;
}

.swiper-item {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 确保 z-paging 在 swiper-item 中正常显示 */
.swiper-item .z-paging {
  width: 100%;
  height: 100%;
}

/* 优化页面列表在swiper中的显示 */
.swiper-item .page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 30rpx;
}

/* 占位内容样式 */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;

  .placeholder-text {
    font-size: 32rpx;
    color: #999;
    text-align: center;
  }
}

/* 骨架屏样式 */
.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 40rpx; /* 增加骨架屏项目之间的间距 */
  justify-content: flex-start;
  min-height: 100vh; /* 铺满整个内容区域 */
  padding: 40rpx; /* 增加内边距，让整体更宽松 */

  .skeleton-item {
    display: flex;
    flex-direction: column;
    /* 让骨架屏和数据卡片高度、样式一致 */
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    /* 骨架屏基础样式 */
    [class*='skeleton-'] {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      border-radius: 8rpx;
      animation: skeleton-loading 1.5s infinite;
    }

    /* 岗位标题和薪资 */
    .skeleton-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx; /* 增加间距 */

      .skeleton-title {
        width: 60%;
        height: 32rpx;
      }

      .skeleton-salary {
        width: 25%;
        height: 32rpx;
      }
    }

    /* 公司信息 */
    .skeleton-company {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx; /* 增加间距 */

      .skeleton-company-name {
        width: 50%;
        height: 24rpx;
      }

      .skeleton-location {
        width: 30%;
        height: 24rpx;
      }
    }

    /* 标签 */
    .skeleton-tags {
      display: flex;
      gap: 20rpx; /* 增加标签间距 */
      margin-bottom: 28rpx; /* 增加底部间距 */

      .skeleton-tag {
        width: 80rpx;
        height: 32rpx;
        border-radius: 16rpx;
      }
    }

    /* HR信息和操作按钮 */
    .skeleton-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 28rpx; /* 增加底部间距 */

      .skeleton-hr {
        display: flex;
        gap: 16rpx; /* 增加间距 */
        align-items: center;

        .skeleton-avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
        }

        .skeleton-hr-info {
          display: flex;
          flex-direction: column;
          gap: 12rpx; /* 增加间距 */

          .skeleton-hr-name {
            width: 120rpx;
            height: 24rpx;
          }

          .skeleton-hr-status {
            width: 80rpx;
            height: 20rpx;
          }
        }
      }

      .skeleton-actions {
        display: flex;
        gap: 16rpx; /* 增加按钮间距 */

        .skeleton-action-btn {
          width: 64rpx;
          height: 64rpx;
          border-radius: 16rpx;
        }
      }
    }

    /* 匹配度进度条 */
    .skeleton-progress {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 28rpx; /* 增加顶部间距 */
      border-top: 2rpx dashed #d8d8d8;

      .skeleton-progress-label {
        width: 140rpx;
        height: 16rpx;
      }

      .skeleton-progress-bar {
        width: 180rpx;
        height: 12rpx;
        border-radius: 6rpx;
      }

      .skeleton-competitors {
        width: 120rpx;
        height: 20rpx;
      }
    }
  }
}

/* 骨架屏加载动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 移除旧的骨架屏样式 */
:deep(.job-skeleton) {
  display: none;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx;

  .empty-content {
    text-align: center;

    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 40rpx;
      opacity: 0.6;
    }

    .empty-text {
      margin-bottom: 16rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
    }

    .empty-desc {
      font-size: 28rpx;
      color: #999;
    }
  }
}
</style>
