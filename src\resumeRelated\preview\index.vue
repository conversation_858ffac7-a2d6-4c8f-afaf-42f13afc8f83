<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img-h">
    <z-paging :loading-more-enabled="false" :refresher-enabled="false">
      <template #top>
        <CustomNavBar :title="isPreview ? '预览简历' : '简历详情'">
          <template #left>
            <wd-icon
              class="back-button"
              color="#333333"
              name="arrow-left"
              size="20"
              @click="handleBack"
            />
          </template>
        </CustomNavBar>
        <view v-if="isPreview" ref="myElement" class="tabbar-list flex-c">
          <view v-for="(item, index) in tabbarlist" :key="index" class="tabbar-item">
            <view
              :class="index == activeIndex ? 'activeItem' : 'normalItem'"
              class="tabbar-name"
              @click="selectItem(index)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
      </template>

      <view v-if="activeIndex === 0" class="onlineRes">
        <view class="onlineRes-list flex-between">
          <view class="onlineRes-left">
            <view class="onlineRes-info flex-c">
              <view class="flex-c m-b-10rpx">
                <view class="name">{{ onlineObj.userName }}</view>
              </view>

              <view
                v-if="onlineObj.isApplicant == 0"
                class="onlineRes-rz flex-c"
                @click="goCheackInfo"
              >
                <view class="name-rz nomal">未认证</view>
                <wd-icon color="#FF0000" name="warning" size="15px"></wd-icon>
              </view>
            </view>
            <view class="onlineRes-my flex items-center m-b-15rpx m-t-6rpx">
              <view class="onlineRes-my-item flex items-center">
                <wd-img :height="16" :src="birthdayIcon" :width="16" class="wd-img" />
                <text class="c-#333333 text-28rpx">{{ onlineObj.age || '**' }} 岁</text>
              </view>

              <view class="onlineRes-my-item flex items-center">
                <wd-img :height="16" :src="icons" :width="16" class="wd-img" />
                <view class="c-#333333 text-28rpx">
                  {{
                    onlineObj?.workYear > 0 && onlineObj?.workYear <= 10
                      ? onlineObj.workYear + '年'
                      : onlineObj.workYear > 10
                        ? '10年以上'
                        : '**'
                  }}
                </view>
              </view>

              <view class="onlineRes-my-item flex items-center">
                <wd-img :height="16" :src="educationIcon" :width="16" class="wd-img" />
                <view class="c-#333333 text-28rpx">{{ onlineObj.xueLi || '**' }}</view>
              </view>
            </view>
            <view class="onlineRes-connect flex-c">
              <view class="flex-c m-right">
                <image
                  class="onlineRes-connect-img"
                  src="@/resumeRelated/img/Group_1171274957.png"
                ></image>
                <view class="onlineRes-connect-name">{{ onlineObj.telephone || '***' }}</view>
              </view>
              <view class="flex-c">
                <image
                  class="onlineRes-connect-img-1"
                  src="@/resumeRelated/img/Group_1171274958.png"
                ></image>
                <view v-if="onlineObj.wxCode" class="onlineRes-connect-name">
                  {{ onlineObj.wxCode || '***' }}
                </view>
                <view v-else class="onlineRes-connect-name c-#4d8fff">***</view>
              </view>
            </view>
          </view>
          <view class="onlineRes-right text-right">
            <!-- <image src="/static/img/1.jpg"></image> -->
            <image
              :class="onlineObj.sex === 1 ? 'border-boy' : 'border-griy'"
              :src="onlineObj.headImgUrl"
              mode="aspectFill"
            ></image>
          </view>
        </view>
        <view class="resume-list flex-between">
          <view class="onlineRes-job-left flex-1">
            <view class="onlineRes-title">求职状态</view>
            <view class="p-t-10rpx text-28rpx">
              {{ onlineObj.seekStatus }}
            </view>
          </view>
          <view class=""></view>
        </view>
        <view class="resume-list">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">个人亮点</view>
            </view>
          </view>
          <view v-if="onlineObj.myLights" class="text-container">
            <view class="mt-10rpx">
              <view class="text-pre-wrap text-28rpx c-#333333 my-lights-text">
                {{ myLightsText }}
                <text
                  v-if="showMyLightsMore"
                  class="view-detail text-28rpx"
                  style="color: #589bff"
                  @click.stop="showMyLightsDetail"
                >
                  展开
                </text>
                <text
                  v-if="showMyLightsCollapse && !showMyLightsMore"
                  class="view-detail text-28rpx"
                  style="color: #589bff"
                  @click.stop="showMyLightsShrink"
                >
                  收起
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="resume-list">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx p-b-10rpx">求职期望</view>
              <!-- <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon> -->
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" class="p-l-10rpx" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>
          <view v-for="(item, index) in onlineObj.jobIntentionList" :key="index">
            <!-- <view class="jobExpectations-qw text-28rpx">
              {{ item.jobType === 1 ? '全职期望' : '兼职期望' }}
            </view> -->
            <view class="jobExpectations-exepress flex-between mt-10rpx">
              <view class="jobExpectations-exepress-left">
                <view class="jobExpectations-exepress-list">
                  <view class="flex-c">
                    <view
                      :class="
                        item.jobType === 1
                          ? 'full-time'
                          : item.jobType === 2 || item.jobType === 3
                            ? 'full-time-other'
                            : ''
                      "
                      class="text-30rpx font-w-500"
                    >
                      {{ item.jobTypeLabel }}
                    </view>
                    <view class="text-name text-salary text-28rpx">
                      {{ item.expectedPositions }}
                    </view>
                    <view class="text-name text-salary text-28rpx">
                      {{
                        item.salaryExpectationStart === '面议'
                          ? '薪资面议'
                          : item.salaryExpectationStart
                      }}
                      <text v-if="item.salaryExpectationEnd">-{{ item.salaryExpectationEnd }}</text>
                      <text v-if="item.salaryExpectationEnd && item.salaryExpectationStart">
                        /月
                      </text>
                    </view>
                  </view>
                  <view class="flex-c">
                    <view class="text-position">{{ item.provinceName }}</view>
                    <view class="text-position text-denery">
                      {{ item.expectedIndustry === '不限' ? '行业不限' : item.expectedIndustry }}
                    </view>
                  </view>
                </view>
              </view>
              <view class="jobExpectations-exepress-right">
                <!-- <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon> -->
              </view>
            </view>
          </view>
        </view>
        <view v-if="onlineObj.educationsList?.length > 0" class="resume-list">
          <view class="jobExpectations-title flex-between m-b-10rpx">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">教育经历</view>
            </view>
          </view>
          <view
            v-for="(item, index) in onlineObj.educationsList"
            :key="index"
            class="education-list flex-between mt-20rpx"
          >
            <view class="education-left flex-c">
              <view class="education-left-img"></view>
              <view class="education-left-xl">
                <view class="flex items-center">
                  <view class="text-32rpx c-#333">{{ item.school }}</view>
                  <view class="education-left-xl-subname text-28rpx c-#333333 m-l-40rpx">
                    {{ item.qualificationLabel }}
                  </view>
                </view>

                <view>
                  <view class="flex items-center">
                    <view
                      class="education-left-xl-subname text-32rpx c-#333333"
                      style="display: inline"
                    >
                      {{ item.major }}
                    </view>
                    <view class="education-left-xl-subname text-28rpx c-#333333 m-l-40rpx">
                      {{ item.startTime.substring(0, 4) }}年-{{ item.endTime.substring(0, 4) }}年
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- <view class="education-left flex-c">
              <view class="education-left-img"></view>
              <view class="education-left-xl">
                <view class="text-28rpx c-#333">{{ item.school }}</view>
                <view>
                  <view>
                    <text
                      class="education-left-xl-subname text-28rpx c-#333333"
                      style="display: inline"
                    >
                      {{ item.qualificationLabel }}
                    </text>
                    <text
                      class="education-left-xl-subname m-l-20rpx text-28rpx c-#333333"
                      style="display: inline"
                    >
                      {{ item.major }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
            <view class="education-right flex-between">
              <view></view>
              <view class="flex-c">
                <view class="time">
                  {{ item.startTime.substring(0, 4) }}-{{ item.endTime.substring(0, 4) }}
                </view>
              </view>
            </view> -->
          </view>
        </view>
        <view v-if="onlineObj.workExperiencesList?.length > 0" class="resume-list">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">工作经历</view>
            </view>
          </view>
          <view>
            <view
              v-for="(item, index) in onlineObj.workExperiencesList"
              :key="index"
              class="work-qw-content mt-20rpx"
            >
              <view class="work-qw-title flex-between">
                <view class="flex-c">
                  <view class="text-28rpx c-#000 font-w-500">{{ item.company }}</view>
                </view>
              </view>
              <view class="flex-between work-qw-line">
                <view class="c-#333333 text-28rpx industry-row">
                  <view>{{ item.industry === '不限' ? '行业不限' : item.industry }}</view>
                  <view>{{ item.positionName }}</view>
                </view>
                <view class="c-#333333 text-28rpx">
                  {{ item.startTime.slice(0, 7) }}至{{ item.endTime.slice(0, 7) }}
                </view>
              </view>
              <view
                v-if="item?.workDescription && item.workDescription.trim() !== ''"
                class="m-t-10rpx"
              >
                <view class="text-container">
                  <view class="text-pre-wrap c-#333333 text-28rpx m-l-[-6rpx] work-content-text">
                    工作内容：{{ getWorkDescriptionText(item, index) }}
                    <text
                      v-if="shouldShowWorkMore(item, index)"
                      class="view-detail text-28rpx"
                      style="color: #589bff"
                      @click.stop="showWorkDetail(index)"
                    >
                      展开
                    </text>
                    <text
                      v-if="shouldShowWorkCollapse(item, index)"
                      class="view-detail text-28rpx"
                      style="color: #589bff"
                      @click.stop="showWorkShrink(index)"
                    >
                      收起
                    </text>
                  </view>
                </view>
              </view>

              <view
                v-if="item?.workPerformance && item.workPerformance.trim() !== ''"
                class="m-t-30rpx"
              >
                <view class="text-container">
                  <view
                    class="text-pre-wrap c-#333333 text-28rpx m-l-[-6rpx] work-performance-text"
                  >
                    工作业绩：{{ getWorkAchievementText(item, index) }}
                    <text
                      v-if="shouldShowWorkAchievementMore(item, index)"
                      class="view-detail text-28rpx"
                      style="color: #589bff"
                      @click.stop="showWorkAchievementDetail(index)"
                    >
                      展开
                    </text>
                    <text
                      v-if="shouldShowWorkAchievementCollapse(item, index)"
                      class="view-detail text-28rpx"
                      style="color: #589bff"
                      @click.stop="showWorkAchievementShrink(index)"
                    >
                      收起
                    </text>
                  </view>
                </view>
              </view>
              <!-- <view class="wx-tag">文案編輯</view> -->
            </view>
          </view>
        </view>
        <view v-if="onlineObj.projectList?.length > 0" class="resume-list">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">项目经历</view>
            </view>
          </view>

          <view
            v-for="(item, index) in onlineObj.projectList"
            :key="index"
            class="work-qw-content mt-20rpx"
          >
            <view class="work-qw-title flex-between">
              <view class="flex-c">
                <view class="text-28rpx c-#000 font-w-500">{{ item.projectName }}</view>
              </view>
              <!-- <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon> -->
            </view>
            <view class="flex-between work-qw-line text-28rpx">
              <view class="c-#333333 text-28rpx">{{ item.takeOffice }}</view>
              <view class="c-#333333 text-28rpx">
                {{ item.startDate.slice(0, 7) }}至{{ item.endDate.slice(0, 7) }}
              </view>
            </view>
            <view class="p-b-10rpx">
              <view class="text-container">
                <view class="text-pre-wrap c-#333333 text-28rpx m-l-[-6rpx] project-content-text">
                  内容：{{ getProjectDescText(item, index) }}
                  <text
                    v-if="shouldShowProjectMore(item, index)"
                    class="view-detail text-28rpx"
                    style="color: #589bff"
                    @click.stop="showProjectDetail(index)"
                  >
                    展开
                  </text>
                  <text
                    v-if="shouldShowProjectCollapse(item, index)"
                    class="view-detail text-28rpx"
                    style="color: #589bff"
                    @click.stop="showProjectShrink(index)"
                  >
                    收起
                  </text>
                </view>
              </view>
            </view>
            <view v-if="item?.projectLinkAddr && item?.projectLinkAddr.trim() !== ''">
              <view class="c-#333333 text-28rpx m-t-10rpx">
                {{ `项目链接：${item.projectLinkAddr}` }}
              </view>
            </view>
          </view>
        </view>
        <view v-if="onlineObj.resumeCertificateVOList?.length > 0" class="resume-list">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">资格证书</view>
            </view>
          </view>
          <view
            v-if="onlineObj.resumeCertificateVOList && onlineObj.resumeCertificateVOList.length > 0"
            class="qualification-list flex-c"
          >
            <view
              v-for="(item, index) in onlineObj.resumeCertificateVOList"
              :key="index"
              class="qualification-tag"
            >
              <!-- <image :src="item.pic" mode=""></image> -->
              <view class="qualification-tag-name">
                {{ item.certificate }}
              </view>
            </view>
          </view>
        </view>
        <view v-if="onlineObj.skills?.length > 0" class="resume-list">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">掌握技能</view>
            </view>
          </view>

          <view
            v-if="onlineObj.skills && onlineObj.skills.length > 0"
            class="qualification-list flex-c"
          >
            <view v-for="(item, index) in onlineObj.skills" :key="index" class="qualification-tag">
              <view class="qualification-tag-name">
                {{ item }}
              </view>
            </view>
          </view>
        </view>
        <view
          v-if="onlineObj.resumeFileVOList?.length > 0 && activeIndex === 0 && isPreview"
          class="resume-list"
        >
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx m-b-20rpx">作品集</view>
            </view>
          </view>
          <view class="Portfolio-input">
            <wd-input v-model="url" no-border placeholder="作品集链接" readonly />
          </view>
        </view>
      </view>
      <view v-if="activeIndex === 1 && isPreview" class="onlineRes onlineRes-card">
        <view class="my-jl-card">
          <view class="p-[0_30rpx]">
            <view class="flex flex-col gap-12rpx">
              <view class="flex items-center gap-26rpx">
                <view
                  :class="cardList?.ActivityStatus === 1 ? 'border-twinkle bg_left_icon_box' : ''"
                >
                  <image :src="cardList.headImgUrl" class="w-82rpx h-82rpx rounded-full"></image>
                </view>
                <view class="flex flex-col gap-6rpx flex-1">
                  <view class="c-#333333 flex items-center">
                    <view class="flex-1 flex items-center">
                      <text class="text-38rpx font-500">
                        {{ cardList.trueName }}
                      </text>
                      <view
                        v-if="cardList?.ActivityStatus"
                        :class="cardList?.ActivityStatus === 1 ? 'c-#0ea500' : 'c-#666'"
                        class="flex items-center text-24rpx"
                      >
                        <view
                          :class="cardList?.ActivityStatus === 1 ? 'bg-#0ea500' : 'bg-#666'"
                          class="mx-8rpx size-6rpx rounded-full"
                        />
                        <text>{{ cardList.ActivityStatus === 1 ? '在线' : '离线' }}</text>
                      </view>
                    </view>
                    <text class="c-#FF8080 text-32rpx font500">
                      {{
                        (() => {
                          const startSalary = formatToKilo(cardList.salaryExpectationStart)
                          const endSalary = formatToKilo(cardList.salaryExpectationEnd)

                          // 如果两个值都不存在（都为面议），只显示一个面议
                          if (startSalary === '面议' && endSalary === '面议') {
                            return '面议'
                          }
                          // 如果两个值都存在，显示范围
                          return `${startSalary}-${endSalary} /月`
                        })()
                      }}
                    </text>
                  </view>
                  <text class="c-#333333 text-24rpx line-clamp-1">
                    {{
                      [cardList.seekStatus, cardList.qualification, cardList.major]
                        .filter(Boolean)
                        .join(' | ')
                    }}
                  </text>
                </view>
              </view>

              <!-- 工作经历 -->
              <view
                v-if="cardList?.workExperienceList?.length"
                class="flex flex-col gap-12rpx relative"
              >
                <view
                  v-for="(item, key) in (isWorkExperienceExpanded
                    ? cardList?.workExperienceList
                    : cardList?.workExperienceList?.slice(0, 1)) ?? []"
                  :key="`workExperienceList-${key}`"
                  class="flex items-center gap-16rpx relative"
                >
                  <image
                    v-if="key === 0"
                    class="w-26rpx h-26rpx"
                    src="@/resumeRelated/img/gongwenbao-2_1.png"
                  />
                  <view v-else class="dot-separator" />

                  <view class="flex items-center flex-1 line-clamp-1">
                    <text class="c-#333333 text-24rpx">{{ item?.company }}</text>
                    <view
                      v-if="item?.company && (item?.workYears || !(item?.workYears ?? 0))"
                      class="dot-separator"
                    />
                    <text class="c-#333333 text-24rpx">
                      {{ !(item?.workYears ?? 0) ? '1年内' : `${item?.workYears}年以上` }}
                    </text>
                  </view>
                  <text
                    v-if="!key && cardList?.workExperienceList?.length > 1"
                    :class="
                      isWorkExperienceExpanded
                        ? 'i-carbon-triangle-solid'
                        : 'i-carbon-triangle-down-solid'
                    "
                    class="c-#000000 text-16rpx"
                    @tap.stop="
                      !key && cardList?.workExperienceList?.length > 1
                        ? toggleWorkExperience()
                        : undefined
                    "
                  />
                </view>
              </view>
            </view>

            <!-- 个人优势和证书 -->
            <view class="flex flex-col gap-12rpx mt-12rpx mb-12rpx">
              <text v-if="cardList?.myLights" class="c-#333333 text-24rpx line-clamp-1">
                {{ cardList.myLights }}
              </text>
              <view class="flex items-center flex-wrap gap-20rpx">
                <view
                  v-for="(item, index) in cardList?.certificate"
                  :key="index"
                  class="bg-#F3F3F3 border-rd-6rpx h-46rpx min-w-150rpx px-20rpx center"
                >
                  <text class="c-#333333 text-24rpx">{{ item }}</text>
                </view>
              </view>
            </view>

            <!-- 匹配度和竞争力指标 -->
            <view
              class="flex items-center h-32rpx gap-10rpx py-30rpx mx--30rpx px-30rpx border-t-1 border-t-dashed border-t-#D8D8D8"
            >
              <view
                class="flex items-center justify-center min-w-168rpx h-32rpx relative p-1px"
                style="
                  clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%);
                  background: linear-gradient(270deg, rgba(255, 86, 86, 1), rgba(81, 150, 253, 1));
                "
              >
                <view
                  class="size-full bg-#ffffff flex items-center px-8rpx gap-4rpx"
                  style="clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%)"
                >
                  <image class="w-88rpx h-20rpx" src="@/static/common/resume-matching.png" />
                  <text class="text-20rpx c-#DF6176 font-500">{{ progressWidth }}%</text>
                </view>
              </view>
              <view class="flex-1 flex items-center">
                <view
                  class="w-182rpx h-24rpx shadow-[0rpx_0rpx_2rpx_0rpx_#FF8A8A] border-rd-full p-1px"
                  style="
                    background: linear-gradient(
                      139deg,
                      rgba(217, 182, 253, 1),
                      rgba(162, 199, 253, 1),
                      rgba(153, 193, 253, 1),
                      rgba(250, 112, 145, 1)
                    );
                  "
                >
                  <view class="w-full h-full bg-white border-rd-full px-6rpx flex items-center">
                    <view
                      :style="{
                        width: `${progressWidth}%`,
                        background:
                          'linear-gradient(315deg, #FF6C8C 0%, #9AC2FD 47%, rgba(90,155,252,0.55) 85%, rgba(141,34,251,0.33) 98%)',
                      }"
                      class="h-14rpx border-rd-full transition-all duration-1000 ease-in-out"
                    />
                  </view>
                </view>
              </view>

              <view class="relative flex items-center gap4rpx">
                <view
                  class="flex items-center justify-center min-w-168rpx h-32rpx p-1px"
                  style="
                    clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%);
                    background: linear-gradient(
                      270deg,
                      rgba(255, 86, 86, 1),
                      rgba(81, 150, 253, 1)
                    );
                  "
                >
                  <view
                    class="size-full bg-#ffffff flex items-center px-8rpx gap-10rpx"
                    style="clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%)"
                  >
                    <image
                      class="w-88rpx h-20rpx"
                      src="@/static/common/resume-competitiveness.png"
                    />
                    <text class="text-20rpx font-bold c-#DF6176">
                      {{ displayActivityTotal }}
                    </text>
                  </view>
                </view>
                <view class="absolute left-88rpx bottom--12rpx">
                  <image
                    class="w-20rpx h-20rpx"
                    src="@/static/common/resume-competitiveness-star.png"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <template #bottom>
        <view v-if="activeIndex === 0 && !isPreview" class="btn_fixed" @click="handleGoChat">
          <view class="btn_box">
            <view class="btn_bg">{{ releaseActivePostIsEnabled ? '开启职位' : '立即沟通' }}</view>
          </view>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryFullData, myCard } from '@/interPost/resume'
import { hrIndexCanSendMsg } from '@/service/hrIndex'
import { resumeUserDetail } from '@/service/resumeDetail'
import type { resumeUserDetailDataInt } from '@/service/resumeDetail/types'
import { numberTokw } from '@/utils/common'
import { formatToKilo } from '@/utils'
import { useActivityStore } from '@/store/activity'
import icons from '@/resumeRelated/img/icons.png'
import birthdayIcon from '@/resumeRelated/img/birthday_icon.png'
import educationIcon from '@/resumeRelated/img/education_icon.png'
import { DICT_IDS } from '@/enum/diction'

const message = useMessage()
const { sendGreetingMessage } = useIMConversation()
const { releaseActivePostIsEnabled } = useReleasePost()
const isPreview = ref(false)
const activeIndex = ref(0)
const value = ref('')
const myElement = ref(null)
const onlineObj = ref<AnyObject>({})
const jobIntentionListObj = ref<AnyObject>({})
const educationsListObj = ref<AnyObject>({})
const url = ref('')
const cardList = ref<AnyObject>({})
const { getDictLabel } = useDictionary()

// 引入activity store
const activityStore = useActivityStore()

// 个人优势文本处理相关变量
const myLightsText = ref('') // 显示的个人优势文本
const myLightsOriginal = ref('') // 原始个人优势文本
const showMyLightsMore = ref(false) // 是否显示"展开"按钮
const showMyLightsCollapse = ref(false) // 是否显示"收起"按钮
const myLightsExpanded = ref(false) // 是否已展开

// 工作内容和业绩分别独立展开/收起状态
const workContentExpandedStates = ref<Record<number, boolean>>({}) // 工作内容展开状态
const workAchievementExpandedStates = ref<Record<number, boolean>>({}) // 工作业绩展开状态
const projectExpandedStates = ref<Record<number, boolean>>({}) // 项目经历展开状态

const list1 = ref([])
const params = ref<AnyObject>({})
const { progressWidth, startProgress } = useProgress()

// 添加工作经历展开状态
const { bool: isWorkExperienceExpanded, toggle: toggleWorkExperience } = useBoolean()

// 添加进度条相关变量
function useProgress() {
  const progressWidth = ref(0)
  const startProgress = () => {
    const randomProgress = Math.floor(Math.random() * 20) + 80
    setTimeout(() => {
      progressWidth.value = randomProgress
    }, 100)
  }
  return { progressWidth, startProgress }
}

// 添加竞争力显示
const displayActivityTotal = computed(() => {
  const total = cardList.value?.activityTotal
  if (!total) {
    return Math.floor(Math.random() * 101) + 100 // 100-200之间随机数
  }
  return total > 999 ? '999+' : total
})

const tabbarlist = ref([
  {
    name: '在线简历',
  },
  {
    name: '首页卡片',
  },
])

async function handleGoChat() {
  if (releaseActivePostIsEnabled.value) {
    uni.navigateTo({
      url: '/sub_business/pages/positionManage/index',
    })
    return
  }
  const { data } = await hrIndexCanSendMsg()
  if (!data) {
    message
      .confirm({
        title: '提示',
        msg: '您还未发布职位，请先发布职位！',
        confirmButtonText: '去发布',
      })
      .then(() => {
        uni.navigateTo({
          url: '/sub_business/pages/release/index',
        })
      })
      .catch(() => {})
    return
  }
  try {
    const { positionInfoId: id = null, positionName = null } = params.value
    const { hxUserInfoVO } = onlineObj.value
    const { username } = hxUserInfoVO
    sendGreetingMessage(username, {
      id,
      positionName,
      hxUserInfoVO,
    })
  } catch (error) {}
}

// 获取简历信息
const getList = async () => {
  await uni.$onLaunched
  const { positionName, ...restParams } = params.value
  const res: any = restParams?.userId
    ? await resumeUserDetail(restParams as resumeUserDetailDataInt)
    : await queryFullData()
  if (res.code === 0) {
    if (
      Array.isArray(res.data.skills) &&
      res.data.skills.length === 1 &&
      res.data.skills[0] === ''
    ) {
      res.data.skills = []
    }
    url.value = res.data.resumeFileVOList.length > 0 ? res.data.resumeFileVOList[0].url : ''
    res.data.jobIntentionList.forEach((ele: any) => {
      ele.salaryExpectationStart =
        ele.salaryExpectationStart === 0 ? '面议' : numberTokw(ele.salaryExpectationStart + '')
      ele.salaryExpectationEnd =
        ele.salaryExpectationEnd === 0 ? '' : numberTokw(ele.salaryExpectationEnd + '')
    })
    res.data.xueLi = await getDictLabel(DICT_IDS.EDUCATION_REQUIREMENT, res.data.xueLi)
    await Promise.all(
      res.data.jobIntentionList.map(async (ele: any) => {
        ele.jobTypeLabel = await getDictLabel(DICT_IDS.JOB_EXPECTATIONS_PROVINCE, ele.jobType)
      }),
    )
    await Promise.all(
      res.data.educationsList.map(async (ele: any) => {
        ele.qualificationLabel = await getDictLabel(
          DICT_IDS.EDUCATION_REQUIREMENT,
          ele.qualification,
        )
      }),
    )
    res.data.seekStatus = await getDictLabel(DICT_IDS.SEEK_STATUS, res.data.seekStatus)
    console.log(res.data.seekStatus, 'res.data')
    if (!res.data?.headImgUrl) {
      res.data.headImgUrl =
        res.data.sex === 1 ? '/static/header/jobhunting1.png' : '/static/header/jobhunting2.png'
    }
    onlineObj.value = res.data
    jobIntentionListObj.value = res.data.jobIntentionList
    educationsListObj.value = res.data.educationsList

    // 处理个人优势文本
    if (res.data.myLights) {
      myLightsOriginal.value = res.data.myLights
      checkMyLightsTextLength()
    }

    // 初始化工作经历展开状态
    if (res.data.workExperiencesList) {
      workContentExpandedStates.value = {}
      res.data.workExperiencesList.forEach((_, index) => {
        workContentExpandedStates.value[index] = false
      })
    }

    // 初始化项目经历展开状态
    if (res.data.projectList) {
      projectExpandedStates.value = {}
      res.data.projectList.forEach((_, index) => {
        projectExpandedStates.value[index] = false
      })
    }
  }
}
// 卡片
const myCardList = async () => {
  const res: any = await myCard()
  if (res.code === 0) {
    cardList.value = res.data
    cardList.value.salaryExpectationStart =
      res.data.salaryExpectationStart === 0
        ? '面议'
        : numberTokw(res.data.salaryExpectationStart + '')
    cardList.value.salaryExpectationEnd =
      res.data.salaryExpectationEnd === 0 ? '面议' : numberTokw(res.data.salaryExpectationEnd + '')
    cardList.value.qualification = await getDictLabel(
      DICT_IDS.EDUCATION_REQUIREMENT,
      res.data.qualification,
    )
    cardList.value.seekStatus = await getDictLabel(DICT_IDS.SEEK_STATUS, res.data.seekStatus)
    if (!res.data?.headImgUrl) {
      res.data.headImgUrl =
        res.data.sex === 1 ? '/static/header/jobhunting1.png' : '/static/header/jobhunting2.png'
    }
  }
}

// 检查个人优势文本长度并设置显示状态
const checkMyLightsTextLength = () => {
  if (!myLightsOriginal.value) {
    myLightsText.value = ''
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
    return
  }

  // 如果原始文本长度大于95字符
  if (myLightsOriginal.value.length > 40) {
    if (!myLightsExpanded.value) {
      // 默认显示简洁内容（前95个字符 + ...）
      myLightsText.value = myLightsOriginal.value.substring(0, 40) + '...'
      showMyLightsMore.value = true // 显示"展开"按钮
      showMyLightsCollapse.value = false // 隐藏"收起"按钮
    } else {
      // 显示完整内容
      myLightsText.value = myLightsOriginal.value
      showMyLightsMore.value = false // 隐藏"展开"按钮
      showMyLightsCollapse.value = true // 显示"收起"按钮
    }
  } else {
    // 文本长度不超过95字符，直接显示全部内容，不显示任何按钮
    myLightsText.value = myLightsOriginal.value
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
  }
}

// 显示个人优势详情
const showMyLightsDetail = () => {
  if (showMyLightsMore.value) {
    myLightsExpanded.value = true
    checkMyLightsTextLength()
  }
}

// 收起个人优势
const showMyLightsShrink = () => {
  if (showMyLightsCollapse.value) {
    myLightsExpanded.value = false
    checkMyLightsTextLength()
  }
}

// 工作经历相关方法
const getWorkDescriptionText = (item: any, index: number) => {
  if (!item.workDescription) return ''

  const isExpanded = workContentExpandedStates.value[index] || false
  if (item.workDescription.length > 37 && !isExpanded) {
    return item.workDescription.substring(0, 37) + '...'
  }
  return item.workDescription
}

const shouldShowWorkMore = (item: any, index: number) => {
  if (!item.workDescription || item.workDescription.length <= 37) return false
  return !workContentExpandedStates.value[index]
}

const shouldShowWorkCollapse = (item: any, index: number) => {
  if (!item.workDescription || item.workDescription.length <= 37) return false
  return workContentExpandedStates.value[index]
}

const showWorkDetail = (index: number) => {
  workContentExpandedStates.value[index] = true
}

const showWorkShrink = (index: number) => {
  workContentExpandedStates.value[index] = false
}
// 工作业绩相关方法
const getWorkAchievementText = (item: any, index: number) => {
  if (!item.workPerformance) return ''
  const isExpanded = workAchievementExpandedStates.value[index] || false
  if (item.workPerformance.length > 37 && !isExpanded) {
    return item.workPerformance.substring(0, 37) + '...'
  }
  return item.workPerformance
}
const shouldShowWorkAchievementMore = (item: any, index: number) => {
  if (!item.workPerformance || item.workPerformance.length <= 37) return false
  return !workAchievementExpandedStates.value[index]
}
const showWorkAchievementDetail = (index: number) => {
  workAchievementExpandedStates.value[index] = true
}
const shouldShowWorkAchievementCollapse = (item: any, index: number) => {
  if (!item.workPerformance || item.workPerformance.length <= 37) return false
  return workAchievementExpandedStates.value[index]
}
const showWorkAchievementShrink = (index: number) => {
  workAchievementExpandedStates.value[index] = false
}

// 项目经历相关方法
const getProjectDescText = (item: any, index: number) => {
  if (!item.projectDescs) return ''

  const isExpanded = projectExpandedStates.value[index] || false
  if (item.projectDescs.length > 37 && !isExpanded) {
    return item.projectDescs.substring(0, 37) + '...'
  }
  return item.projectDescs
}

const shouldShowProjectMore = (item: any, index: number) => {
  if (!item.projectDescs || item.projectDescs.length <= 37) return false
  return !projectExpandedStates.value[index]
}

const shouldShowProjectCollapse = (item: any, index: number) => {
  if (!item.projectDescs || item.projectDescs.length <= 37) return false
  return projectExpandedStates.value[index]
}

const showProjectDetail = (index: number) => {
  projectExpandedStates.value[index] = true
}

const showProjectShrink = (index: number) => {
  projectExpandedStates.value[index] = false
}

// 处理薪资显示
const handleSalaryDisplay = (workSalaryBegin: any, workSalaryEnd: any) => {
  const isBeginNegotiable = workSalaryBegin === '面议'
  const isEndNegotiable = workSalaryEnd === '面议'
  if (isBeginNegotiable && isEndNegotiable) {
    return '面议'
  }
  if (!workSalaryBegin && !workSalaryEnd) {
    return ''
  }

  if (isBeginNegotiable || isEndNegotiable) {
    return isBeginNegotiable ? workSalaryEnd || '面议' : workSalaryBegin || '面议'
  }

  if (workSalaryBegin && !workSalaryEnd) return `${workSalaryBegin}`
  if (!workSalaryBegin && workSalaryEnd) return `${workSalaryEnd}`

  return `${workSalaryBegin}-${workSalaryEnd}`
}
const selectItem = (index) => {
  activeIndex.value = index
  console.log(activeIndex.value, 'activeIndex===')
}

// 添加缺失的goCheackInfo函数
const goCheackInfo = () => {
  if (!params.value) {
    const idCard = ''
    const trueName = onlineObj.value.userName
    uni.navigateTo({
      url: `/setting/identityAuth/index?idCard=${idCard}&trueName=${trueName}`,
    })
  }
}

// 处理返回按钮点击
const handleBack = () => {
  // 如果有userId，触发活动增量
  const userId = params.value?.userId
  if (userId) {
    activityStore.incrementActivity(userId.toString())
  }

  // 返回上一页
  uni.navigateBack()
}

onLoad(async (options) => {
  if (options.isPreview) {
    isPreview.value = true

    myCardList()
  }
  if (options.hrDetailItem) {
    params.value = JSON.parse(decodeURIComponent(options.hrDetailItem))
  }
  await getList()
})

onMounted(() => {
  startProgress()
})
</script>

<style lang="scss" scoped>
::v-deep .wd-picker__value {
  font-size: 28rpx;
  color: #333;
}

::v-deep .wd-input__value {
  padding: 20rpx 20rpx !important;
  background-color: #e3e3e3;
  border-radius: 20rpx;
}

::v-deep .wd-input {
  background-color: transparent !important;
}

::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-bottom: 0 !important;
  padding-left: 0 !important;
  background: transparent !important;
}

::v-deep .wd-picker__arrow {
  display: none;
}

.time {
  font-size: 26rpx;
  color: #333333;
}

// 确保图标和文本垂直居中对齐
.wd-img {
  display: flex;
  flex-shrink: 0; // 防止图标被压缩
  align-items: center;
}

.border-boy {
  border: 3rpx solid #3e9cff;
}

.border-griy {
  border: 3rpx solid rgba(255, 190, 190, 1);
}

.activeColor {
  background-color: #f3f3f3 !important;
}

.start_ICON_item-1 {
  width: 35rpx;
  height: 35rpx;
}

.activeText {
  color: #333333 !important;
}

.active {
  color: #000 !important;
}

.nomal {
  color: #ff0000 !important;
}

.onlineRes-rz {
  margin-left: 30rpx;
}

.name-rz {
  margin-right: 4rpx;
  font-size: 26rpx;
  color: #ff0000;
}

.onlineRes-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.onlineRes-subtitle {
  font-size: 24rpx;
  color: #666666;
}

.work-qw-line {
  line-height: 50rpx;

  .industry-row {
    display: flex;
    gap: 20rpx;
    align-items: center;
  }
}

.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 40rpx 20rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.my-jl-card {
  padding: 20rpx 20rpx 0 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

  .my-jl-card-left {
    justify-content: flex-start;
    width: 50%;

    .my-jl-card-left-header {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .my-jl-card-left-item {
      margin-left: 10rpx;

      .card-name {
        max-width: 228rpx;
        overflow: hidden;
        font-size: 28rpx;
        font-weight: 500;
        color: #555;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .my-jl-card-right {
    flex: 1;
    flex-wrap: wrap;
    justify-content: right;

    .my-jl-card-right-btn {
      padding: 2rpx 13rpx;
      margin-bottom: 15rpx;
      margin-left: 10rpx;
      background-color: #f3f3f3;
      border-radius: 10rpx;

      .pic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }

  .my-jl-card-cunstrct {
    line-height: 52rpx;

    .my-jl-card-cunstrct-img {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.tabbar-list {
  justify-content: center;
  padding: 0rpx 40rpx 0rpx;

  .tabbar-item {
    width: 200rpx;
    padding: 20rpx;
    text-align: center;
  }

  .normalItem {
    color: #aaaaaa;
    border-bottom: 0rpx solid transparent;
  }

  .activeItem {
    position: relative;
    font-weight: 500;
    color: #333;
  }

  .activeItem::after {
    position: absolute;
    bottom: -10rpx;
    left: 0;
    width: 160rpx;
    height: 8rpx;
    content: '';
    background-image: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
    border-radius: 20rpx;
  }
}

.my-lights-text {
  display: block;
  line-height: 1.2 !important;
  text-indent: 0;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.my-lights-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.work-content-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.work-performance-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.project-content-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.onlineRes-card {
  padding: 20rpx 30rpx 0 30rpx !important;
}

.onlineRes {
  padding: 40rpx 40rpx 120rpx;

  .onlineRes-list {
    padding-bottom: 36rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-left {
      width: 75%;

      .onlineRes-info {
        .name {
          font-size: 42rpx;
          font-weight: bold;
          color: #000000;
        }

        .onlineRes-rz {
          margin-left: 50rpx;

          .name-rz {
            margin-right: 4rpx;
            font-size: 26rpx;
            color: #ff0000;
          }
        }
      }

      .onlineRes-my {
        font-size: 26rpx;
        line-height: 50rpx;
        color: #333333;

        .onlineRes-my-item {
          display: flex;
          gap: 8rpx;
          align-items: center;
          margin-right: 36rpx;

          text {
            display: flex;
            align-items: center;
            line-height: 1.2; // 设置合适的行高
          }
        }
      }

      .onlineRes-connect {
        .m-right {
          margin-right: 50rpx;
        }

        .onlineRes-connect-img {
          width: 28rpx !important;
          height: 28rpx !important;
        }

        .onlineRes-connect-img-1 {
          width: 30rpx;
          height: 26rpx;
        }

        .onlineRes-connect-name {
          padding-left: 5rpx;
          font-size: 28rpx;
          color: #333333;
        }
      }
    }

    .onlineRes-right {
      width: 25%;

      image {
        width: 120rpx;
        height: 120rpx;
        margin: 0 0 0 auto;
        border-radius: 50%;
      }
    }
  }

  .onlineRes-job {
    padding: 20rpx 0 5rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-job-left {
      .onlineRes-time {
        padding-top: 10rpx;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }

  .resume-list {
    padding: 42rpx 0;
    border-bottom: 1rpx solid #d7d6d6;
  }

  .jobExpectations-qw {
    line-height: 60rpx;
  }

  .jobExpectations-exepress-left {
    .text-name {
      line-height: 60rpx;
    }

    .text-salary {
      padding-left: 20rpx;
    }

    .text-position {
      font-size: 28rpx;
      color: #333333;
    }

    .text-denery {
      padding-left: 40rpx;
    }
  }

  .work-qw {
    padding: 20rpx 0 10rpx 0;
    border-bottom: 1rpx solid #d7d6d6;

    .jobExpectations-exepress {
      .jobExpectations-exepress-left {
        .text-name {
          line-height: 60rpx;
        }

        .text-salary {
          padding-left: 20rpx;
        }

        .text-position {
          font-size: 24rpx;
          color: #333333;
        }

        .text-denery {
          padding-left: 40rpx;
        }
      }
    }

    .work-qw-title {
      line-height: 60rpx;
    }

    .wx-tag {
      width: 160rpx;
      padding: 2rpx 5rpx;
      font-size: 24rpx;
      color: #333333;
      text-align: center;
      background-color: #e5e5e5;
      border-radius: 10rpx;
    }
  }

  .education {
    padding-top: 30rpx;
    padding-bottom: 10rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .education-list {
      padding: 0rpx 0rpx 30rpx;

      .education-left {
        width: 70%;

        .education-left-img {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
          background-image: url('@/static/img/Group_1171274967.png');
          background-position: 100% 100%;
          background-size: 100% 100%;
        }

        .education-left-xl {
          .education-left-xl-subname {
            color: #333333;
          }
        }
      }

      .education-right {
        width: 30%;
      }
    }
  }

  .qualification-list {
    flex-wrap: wrap !important;
    width: 100%;
    padding: 20rpx 0;

    .qualification-tag {
      // width: 33%;
      padding-top: 10rpx;
      text-align: center;

      .qualification-tag-name {
        padding: 10rpx 20rpx;
        margin-right: 10rpx;
        font-size: 28rpx;
        color: #333333;
        background-color: #d9d9d9;
        border-radius: 10rpx;
      }
    }
  }

  .Portfolio {
    padding: 20rpx 0rpx 0rpx;

    .Portfolio-subtitle {
      padding-bottom: 40rpx;
    }

    .Portfolio-input {
      padding-bottom: 40rpx;
    }

    .Portfolio-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .Portfolio-upload-img {
        width: 120rpx;
        height: 120rpx;
      }
    }
  }

  .text-container {
    display: inline;

    .view-detail {
      display: inline;
      margin-left: 10rpx;
      color: #457ae6;
      white-space: nowrap;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  :deep(.wd-tooltip__inner) {
    padding-right: 13rpx;
    font-size: 22rpx;
    text-align: left;
    white-space: normal;
    direction: ltr;
  }

  .work-content-text {
    display: block;
    padding-left: 6rpx;
    line-height: 1.2 !important;
    text-indent: -6rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .work-performance-text {
    display: block;
    padding-left: 6rpx;
    line-height: 1.2 !important;
    text-indent: -6rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .project-content-text {
    display: block;
    padding-left: 6rpx;
    line-height: 1.2 !important;
    text-indent: -6rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  // 添加在线状态闪烁效果
  .border-twinkle {
    position: relative;

    &::before {
      position: absolute;
      top: -2rpx;
      right: -2rpx;
      bottom: -2rpx;
      left: -2rpx;
      z-index: -1;
      content: '';
      background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
      border-radius: 50rpx;
      animation: twinkle 6s infinite;
    }
  }

  .bg_left_icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 86rpx;
    height: 86rpx;
    border: 3rpx solid #0ea500;
    border-radius: 50rpx;
  }

  @keyframes twinkle {
    0% {
      opacity: 0.3;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.3;
    }
  }

  // 添加点分隔符样式
  .dot-separator {
    flex-shrink: 0;
    width: 6rpx;
    height: 6rpx;
    margin: 4rpx 11rpx 0 11rpx;
    background-color: #666;
    border-radius: 50%;
  }

  .full-time {
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 84rpx !important;
    height: 40rpx !important;
    padding: 0 10rpx;
    color: #000000 !important;
    border: 1rpx solid #000000;
    border-radius: 8rpx;
  }

  .full-time-other {
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 84rpx !important;
    height: 40rpx !important;
    padding: 0 10rpx;
    color: #888888 !important;
    border: 1rpx solid #888888;
    border-radius: 8rpx;
  }
}

:deep(.wd-input__inner) {
  font-size: 28rpx !important;
}
</style>
