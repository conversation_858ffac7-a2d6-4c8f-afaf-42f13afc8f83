<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">开发票</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>

    <view class="page">
      <view class="invoice-card">
        <!-- 蓝色背景头部 -->
        <view class="invoice-header-blue">
          <text class="invoice-title">开发票</text>
        </view>
        <!-- 白色内容区域 -->
        <view class="invoice-content">
          <view class="row type-row">
            <text class="label">
              发票类型
              <text class="star">*</text>
            </text>
            <view class="type-switch">
              <view
                :class="['type-btn', form.invoiceType === 1 ? 'active' : '']"
                @click="handleSelectInvoiceType(1)"
              >
                电子普通发票
              </view>
            </view>
          </view>
          <view class="row type-row">
            <text class="label">
              抬头类型
              <text class="star">*</text>
            </text>
            <view class="type-switch">
              <view
                :class="['type-btn', form.headUpType === 1 ? 'active' : '']"
                @click="handleSelectHeadUpType(1)"
              >
                企业单位
              </view>
              <view
                :class="['type-btn', form.headUpType === 2 ? 'active' : '']"
                @click="handleSelectHeadUpType(2)"
              >
                个人/非企业
              </view>
            </view>
          </view>
          <view class="row">
            <text class="label">
              发票金额
              <text class="star">*</text>
            </text>
            <input
              v-model="form.invoiceMoney"
              class="input value money"
              disabled
              placeholder="请输入金额"
              type="number"
            />
          </view>
          <view v-if="form.headUpType === 1" class="row">
            <text class="label">
              公司抬头
              <text class="star">*</text>
            </text>
            <input
              v-model="form.name"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入公司抬头"
            />
          </view>
          <view v-if="form.headUpType === 2" class="row">
            <text class="label">
              个人抬头
              <text class="star">*</text>
            </text>
            <input
              v-model="form.name"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入个人抬头"
            />
          </view>
          <view v-if="form.headUpType === 1" class="row">
            <text class="label">
              公司税号
              <text class="star">*</text>
            </text>
            <input
              v-model="form.creditCode"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入公司税号"
            />
          </view>
          <view class="other-row-tip">
            <text class="tip-text">电子发票开具后不可作废，请确保名称全称、税号正确。</text>
          </view>
        </view>
      </view>

      <view class="other-card">
        <view class="other-card-content">
          <view class="other-row">
            <text class="label">
              邮箱
              <text class="star">*</text>
            </text>
            <input
              v-model="form.postBox"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入邮箱"
            />
          </view>
          <view class="other-row">
            <text class="label">手机</text>
            <input
              v-model="form.phone"
              :disabled="isDetail"
              class="input value"
              placeholder="请输入手机号码"
            />
          </view>
          <view v-if="!isDetail" class="other-row-tip">
            <text class="tip-text">*符号为必填项</text>
          </view>
        </view>
      </view>
      <view v-if="!isDetail" class="time-card">
        <text class="c-#888888 text-28rpx">
          电子发票将在1-7个工作日内发送至您的邮箱，请注意查收并妥善保存。感谢您的支持！
        </text>
      </view>

      <view v-if="isDetail" class="time-card">
        <text class="label">开票时间</text>
        <text class="time-value">{{ invoiceCreatedTime }}</text>
      </view>
    </view>
  </z-paging>
  <view v-if="!isDetail" class="footer-bar-fixed">
    <view class="footer-bar-inner">
      <view class="footer-info-col">
        <view class="footer-info-row">
          <text class="footer-label">合计:</text>
          <text class="footer-amount">￥ {{ displayInvoiceMoney }}</text>
        </view>
        <view v-if="form.dealId.length > 0" class="footer-record">
          {{ `${form.dealId.length}笔充值记录` }}
        </view>
      </view>
      <button class="footer-btn-fixed" @click="handleSubmit">开发票</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { type ConfigProviderThemeVars } from 'wot-design-uni'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import { addInvoiceRecord, getInvoiceRecordById } from '@/service/walletInvoice'
import { Money, toFixed } from '@/utils/precision'

const invoiceCreatedTime = ref<string>('')
const isDetail = ref<boolean>(false)
const totalAmount = ref<any>(null)

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
    marginTop: '0rpx',
    marginBottom: isDetail.value ? '200rpx' : '0rpx',
  },
})

const { clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}

const form = ref({
  dealId: [],
  invoiceType: 1,
  headUpType: 1,
  invoiceMoney: null,
  creditCode: '',
  name: '',
  companyPhone: '',
  bankCard: '',
  postBox: '',
  phone: '',
})

const displayInvoiceMoney = computed(() => {
  if (!form.value.invoiceMoney) return '0.00'
  return toFixed(form.value.invoiceMoney, 2)
})

function handleSelectInvoiceType(type: number) {
  if (isDetail.value) return
  form.value.invoiceType = type
}

function handleSelectHeadUpType(type: number) {
  if (isDetail.value) return
  form.value.headUpType = type
  form.value.name = ''
  form.value.creditCode = ''
}

const resetForm = () => {
  form.value = {
    dealId: [],
    invoiceType: 1,
    headUpType: 1,
    invoiceMoney: null,
    creditCode: '',
    name: '',
    companyPhone: '',
    bankCard: '',
    postBox: '',
    phone: '',
  }
}

const handleSubmit = async () => {
  if (!form.value.invoiceMoney) {
    uni.showToast({ title: '发票金额不能为空', icon: 'none' })
    return
  }
  if (!form.value.name) {
    if (form.value.headUpType === 1) {
      uni.showToast({ title: '请输入公司抬头', icon: 'none' })
      return
    } else if (form.value.headUpType === 2) {
      uni.showToast({ title: '请输入个人抬头', icon: 'none' })
      return
    }
  }

  if (!form.value.creditCode && form.value.headUpType === 1) {
    uni.showToast({ title: '请输入公司税号', icon: 'none' })
    return
  }
  if (!form.value.postBox) {
    uni.showToast({ title: '请输入邮箱', icon: 'none' })
    return
  }
  if (!form.value.invoiceType) {
    uni.showToast({ title: '请选择发票类型', icon: 'none' })
    return
  }
  if (!form.value.headUpType) {
    uni.showToast({ title: '请选择抬头类型', icon: 'none' })
    return
  }
  const submitData = {
    ...form.value,
    invoiceMoney: form.value.invoiceMoney ? Money.yuanToCent(form.value.invoiceMoney) : 0,
  }
  const res: any = await addInvoiceRecord({ ...submitData }).then((res: any) => {
    if (res.code === 0) {
      uni.showToast({ title: '提交成功', icon: 'success' })
      isDetail.value = false
      uni.navigateBack()
      resetForm()
    } else {
      uni.showToast({ title: res.msg, icon: 'none' })
    }
  })
}

const getInvoiceRecordData = async (id: any) => {
  const res: any = await getInvoiceRecordById({ id })
  if (res.code === 0) {
    res.data.invoiceMoney = Money.centToYuan(res.data.invoiceMoney).toFixed(2)
    invoiceCreatedTime.value = res.data.createTime
    form.value = res.data
  } else {
    uni.showToast(res.msg)
  }
}

function handleClickLeft() {
  uni.navigateBack()
}

onLoad(async (options: any) => {
  if (options.id) {
    isDetail.value = true
    await getInvoiceRecordData(options.id)
  }
  if (options.totalAmount) {
    form.value.invoiceMoney = Money.centToYuan(options.totalAmount).toFixed(2)
  }
  if (options.selectedDealIds) {
    try {
      const selectedDealIds = JSON.parse(decodeURIComponent(options.selectedDealIds))
      form.value.dealId = selectedDealIds
    } catch (error) {
      console.error(error)
    }
  }
  pagingRef.value?.reload()
})

onMounted(async () => {
  pagingRef.value?.reload()
})

onBeforeUnmount(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  padding: 20rpx 0;
}
.invoice-card {
  margin: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.invoice-header-blue {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140rpx;
  background: linear-gradient(135deg, #4285f4 0%, #6fa5ff 100%);
  position: relative;
}

.invoice-header-blue::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 20rpx solid transparent;
  border-right: 20rpx solid transparent;
  border-top: 20rpx solid #6fa5ff;
}

.invoice-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  letter-spacing: 2rpx;
}

.invoice-content {
  padding: 40rpx 32rpx 32rpx;
  background: #fff;
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.row:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.star {
  margin-left: 4rpx;
  color: #ff4d4f;
}

.value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

.money {
  font-weight: 600;
  color: #333;
}

.placeholder {
  color: #bbb;
}

.type-switch {
  display: flex;
  gap: 16rpx;
}

.type-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: normal;
  color: #4285f4;
  background: #e8f0fe;
  border: 2rpx solid #4285f4;
  border-radius: 8rpx;
  transition: all 0.2s;
}

.type-btn.active {
  color: #fff;
  background: #4285f4;
  border-color: #4285f4;
}

.input {
  min-width: 420rpx;
  height: 44rpx;
  padding: 0;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  background: transparent;
  border: none;
  outline: none;
}

.input::placeholder {
  font-size: 26rpx;
  color: #999;
}

.other-card {
  min-height: 400rpx;
  margin: 32rpx 24rpx 0 24rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}

.other-card-content {
  padding: 45rpx;
}

.other-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72rpx;
  padding: 56rpx 0;
  font-size: 24rpx;
  color: #000000;
  border-bottom: 1rpx solid #e1e1e1;
}

.other-row-tip {
  margin-top: 24rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;

  .tip-text {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

// .other-row:last-child {
//   border-bottom: none;
// }
.tip-row {
  justify-content: flex-end;
  height: 40rpx;
  border-bottom: none;
}

.tip {
  margin-top: 2rpx;
  font-size: 22rpx;
  color: #bbb;
}

.time-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 130rpx;
  padding: 0 24rpx;
  margin: 28rpx 24rpx;
  font-size: 28rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.06);
}

.time-value {
  font-weight: 500;
  color: #222;
}

.footer-bar {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 0 24rpx;
  margin: 36rpx 0 0 0;
}

.footer-left {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
}

.footer-label {
  font-size: 28rpx;
  color: #222;
}

.footer-amount {
  margin-left: 4rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a73e8;
}

.footer-record {
  margin-top: 4rpx;
  font-size: 22rpx;
  color: #bbb;
}

.footer-btn {
  width: 180rpx;
  height: 72rpx;
  margin-left: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(90deg, #6fa5ff 0%, #5d7cff 100%);
  border: none;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx 0 rgba(95, 136, 255, 0.12);
}

.footer-bar-fixed {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  padding: 0 24rpx 24rpx 24rpx;
  pointer-events: none;
  background: transparent;
}

.footer-bar-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx 24rpx 32rpx;
  pointer-events: auto;
  background: #fafbfc;
  border-radius: 24rpx;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}

.footer-info-col {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
}

.footer-info-row {
  display: flex;
  align-items: baseline;
  font-size: 32rpx;
}

.footer-label {
  margin-right: 4rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #111;
}

.footer-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #1976ed;
}

.footer-record {
  margin-top: 8rpx;
  margin-left: 2rpx;
  font-size: 24rpx;
  color: #bbb;
}

.footer-btn-fixed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 172rpx;
  height: 96rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #1677ff;
  border-radius: 24rpx;
}
</style>
